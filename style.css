/* 全局样式和字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
    --bg-color: #0f172a;
    --primary-color: #1e293b;
    --secondary-color: #334155;
    --accent-color: #3b82f6;
    --text-color: #f1f5f9;
    --text-secondary: #94a3b8;
    --highlight-color: #10b981;
    --border-color: #475569;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --font-family: 'Inter', sans-serif;

    /* Mutation colors */
    --gold-color: #fbbf24;
    --diamond-color: #60a5fa;
    --galactic-color: #a855f7;
    --blood-color: #ef4444;
    --candy-color: #ec4899;
    --lava-color: #f97316;
    --rainbow-color: linear-gradient(45deg, #ef4444, #f97316, #eab308, #22c55e, #3b82f6, #8b5cf6, #ec4899);
}

* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-color) 0%, #1e293b 100%);
    color: var(--text-color);
    margin: 0;
    padding: 20px;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 30px;
    background-color: var(--primary-color);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
}

header {
    text-align: center;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 30px;
    margin-bottom: 40px;
}

h1 {
    color: var(--accent-color);
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.2em;
    font-weight: 500;
}

h2 {
    color: var(--highlight-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 15px;
    margin-top: 0;
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

h3 {
    color: var(--text-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.calculator-section {
    background-color: var(--secondary-color);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

/* Controls Grid */
.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

/* Select Styles */
.brainrot-selector, .luck-selector {
    padding: 12px 16px;
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.brainrot-selector:focus, .luck-selector:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    gap: 10px;
}

.checkbox-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--accent-color);
}

/* Mutation Buttons */
.mutation-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 12px;
    width: 100%;
}

.mutation-btn {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mutation-btn:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mutation-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
}

.mutation-btn[data-mutation="Gold"].active {
    background-color: var(--gold-color);
    border-color: var(--gold-color);
    color: #000;
}

.mutation-btn[data-mutation="Diamond"].active {
    background-color: var(--diamond-color);
    border-color: var(--diamond-color);
}

.mutation-btn[data-mutation="Galactic"].active {
    background-color: var(--galactic-color);
    border-color: var(--galactic-color);
}

.mutation-btn[data-mutation="Blood"].active {
    background-color: var(--blood-color);
    border-color: var(--blood-color);
}

.mutation-btn[data-mutation="Candy"].active {
    background-color: var(--candy-color);
    border-color: var(--candy-color);
}

.mutation-btn[data-mutation="Lava"].active {
    background-color: var(--lava-color);
    border-color: var(--lava-color);
}

.mutation-btn[data-mutation="Rainbow"].active {
    background: var(--rainbow-color);
    border-color: transparent;
    color: white;
    font-weight: 700;
}

/* Brainrot Display Card */
.brainrot-display-card {
    background: linear-gradient(135deg, var(--bg-color) 0%, var(--secondary-color) 100%);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.brainrot-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.brainrot-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.brainrot-emoji {
    font-size: 3rem;
    line-height: 1;
}

.brainrot-rarity {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.brainrot-rarity:contains("Common") { background-color: #6b7280; }
.brainrot-rarity:contains("Rare") { background-color: #3b82f6; }
.brainrot-rarity:contains("Epic") { background-color: #8b5cf6; }
.brainrot-rarity:contains("Legendary") { background-color: #f59e0b; }
.brainrot-rarity:contains("Mythic") { background-color: #ef4444; }
.brainrot-rarity:contains("God") { background-color: #10b981; }
.brainrot-rarity:contains("Secret") { background: var(--rainbow-color); }

.brainrot-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--primary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 700;
    color: var(--text-color);
    font-size: 1rem;
}

.stat-value.highlight {
    color: var(--highlight-color);
    font-size: 1.1rem;
}

/* Mutation Chances Section */
.mutation-chances-section {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
}

.chances-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.chances-table td {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
}

.chances-table td:first-child {
    color: var(--text-color);
    font-weight: 600;
}

.chances-table td:last-child {
    text-align: right;
    color: var(--highlight-color);
    font-weight: 700;
}

.server-luck-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

#server-luck-display {
    color: var(--accent-color);
    font-weight: 700;
}

/* Tip Section */
.tip-section {
    background: linear-gradient(135deg, var(--accent-color)20, var(--highlight-color)20);
    border: 1px solid var(--accent-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    font-weight: 500;
    color: var(--text-color);
}

/* Info Section */
.info-section {
    background-color: var(--secondary-color);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.info-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 20px;
    font-size: 1rem;
}

.info-section p:last-child {
    margin-bottom: 0;
}

/* FAQ Section */
.faq-section {
    background-color: var(--secondary-color);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.faq-item {
    background-color: var(--primary-color);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.faq-item h3 {
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.faq-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

footer p {
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.footer-link {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--highlight-color);
    text-decoration: underline;
}

.footer-separator {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Policy Pages */
.policy-section {
    background-color: var(--secondary-color);
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    line-height: 1.7;
}

.policy-section h2 {
    color: var(--highlight-color);
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.policy-section h3 {
    color: var(--accent-color);
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.policy-section ul {
    margin: 15px 0;
    padding-left: 25px;
}

.policy-section li {
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.policy-section p {
    color: var(--text-secondary);
    margin-bottom: 15px;
}

.policy-section a {
    color: var(--accent-color);
    text-decoration: none;
}

.policy-section a:hover {
    color: var(--highlight-color);
    text-decoration: underline;
}

.back-link {
    margin-top: 40px;
    margin-bottom: 20px;
    text-align: center;
    padding: 20px;
}

.btn-back {
    display: inline-block;
    padding: 12px 24px;
    background-color: #3b82f6;
    color: #ffffff;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #3b82f6;
}

.btn-back:hover {
    background-color: #10b981;
    border-color: #10b981;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Utility Classes */
.hidden {
    display: none;
}

.highlight {
    color: var(--highlight-color);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    .controls-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* 手机端：变异按钮一列布局 */
    .mutation-buttons {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: repeat(8, 1fr);
        gap: 10px;
        width: 100%;
    }

    .mutation-btn {
        width: 100%;
        height: auto;
        padding: 14px 20px;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .brainrot-header {
        flex-direction: column;
        text-align: center;
    }

    .brainrot-emoji {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 15px;
    }

    .calculator-section, .faq-section {
        padding: 20px;
    }

    .brainrot-emoji {
        font-size: 2rem;
    }

    .brainrot-name {
        font-size: 1.2rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.brainrot-display-card {
    animation: fadeIn 0.5s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}