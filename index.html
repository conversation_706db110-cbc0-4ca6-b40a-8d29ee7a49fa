<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Roblox Steal a Brainrot Calculator - Calculate income, mutation profit, and break-even time for all Brainrots. Includes server luck and bloodmoon event features.">
    <meta name="keywords" content="Steal a Brainrot, Roblox calculator, Brainrot income, mutation calculator, bloodmoon event">
    
    <title>Roblox Steal a Brainrot Calculator</title>
    
    <link rel="stylesheet" href="style.css">
</head>
<body>

    <div class="container">
        <header>
            <h1>Roblox Steal a Brainrot Calculator</h1>
            <p class="subtitle">Calculate income, mutation profit, and break-even time</p>
        </header>

        <main>
            <section class="calculator-section">
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="brainrot-select"><strong>Select Brainrot:</strong></label>
                        <select id="brainrot-select" class="brainrot-selector">
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="server-luck-select"><strong>Server Luck:</strong></label>
                        <select id="server-luck-select" class="luck-selector">
                            <option value="1">1× (Normal)</option>
                            <option value="2">2×</option>
                            <option value="3">3×</option>
                            <option value="4">4×</option>
                            <option value="5">5×</option>
                            <option value="6">6× (Max)</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="bloodmoon-checkbox">
                            <span class="checkmark"></span>
                            Bloodmoon event active
                        </label>
                    </div>
                    
                    <div class="control-group">
                        <label><strong>Mutation:</strong></label>
                        <div class="mutation-buttons">
                            <button class="mutation-btn" data-mutation="Regular">Regular: 1.0×</button>
                            <button class="mutation-btn" data-mutation="Gold">Gold: 1.25×</button>
                            <button class="mutation-btn" data-mutation="Diamond">Diamond: 1.5×</button>
                            <button class="mutation-btn" data-mutation="Galactic">Galactic: 3.0×</button>
                            <button class="mutation-btn" data-mutation="Blood">Blood: 2.0×</button>
                            <button class="mutation-btn" data-mutation="Candy">Candy: 4.0×</button>
                            <button class="mutation-btn" data-mutation="Lava">Lava: 6.0×</button>
                            <button class="mutation-btn" data-mutation="Rainbow">Rainbow: 10.0×</button>
                        </div>
                    </div>
                </div>
                
                <div class="brainrot-display-card">
                    <div id="brainrot-display" class="brainrot-header">
                        <!-- Brainrot info will be populated by JavaScript -->
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Cost:</span>
                            <span id="cost-display" class="stat-value">$0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Sell Value:</span>
                            <span id="sell-value-display" class="stat-value">$0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Mutation:</span>
                            <span id="mutation-display" class="stat-value">Regular</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Money/sec:</span>
                            <span id="income-display" class="stat-value highlight">$0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Profit/hr:</span>
                            <span id="profit-hour-display" class="stat-value highlight">$0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Break-even:</span>
                            <span id="break-even-display" class="stat-value">N/A</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Mutate Cost:</span>
                            <span id="mutate-cost-display" class="stat-value">$5M</span>
                        </div>
                    </div>
                </div>
                
                <div class="mutation-chances-section">
                    <h3>Mutation Chances:</h3>
                    <table id="mutation-chances-table" class="chances-table">
                        <tbody>
                            <!-- Mutation chances will be populated by JavaScript -->
                        </tbody>
                    </table>
                    <p class="server-luck-info">Server Luck: <span id="server-luck-display">1×</span></p>
                </div>
                
                <div id="tip-display" class="tip-section">
                    <!-- Tips will be populated by JavaScript -->
                </div>
            </section>
            
            <section class="faq-section">
                <h2>Frequently Asked Questions</h2>
                <div class="faq-grid">
                    <div class="faq-item">
                        <h3>What does this calculator do?</h3>
                        <p>It shows how much money each Brainrot makes, the effect of mutations, and how long it takes to break even.</p>
                    </div>
                    <div class="faq-item">
                        <h3>What are mutations and how do they affect income?</h3>
                        <p>Mutations multiply income: Gold (×1.25), Diamond (×1.5), Galactic (×3.0), Blood (×2.0), Candy (×4.0), Lava (×6.0), Rainbow (×10.0). Higher luck and Bloodmoon events boost your chances for rare mutations.</p>
                    </div>
                    <div class="faq-item">
                        <h3>How do I increase my chances of getting rare mutations?</h3>
                        <p>Use higher server luck (up to 6×) and trigger Bloodmoon events. This significantly boosts rare mutation odds.</p>
                    </div>
                    <div class="faq-item">
                        <h3>What's the strongest Brainrot right now?</h3>
                        <p>Garama and Madundung with a Rainbow mutation delivers the highest income. It's extremely rare and powerful.</p>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2025 Steal a Brainrot Calculator. Data based on community research, for reference only.</p>
        </footer>

    </div>

    <script src="script.js"></script>
</body>
</html>
