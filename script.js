// 当整个页面内容加载完毕后执行脚本
document.addEventListener('DOMContentLoaded', () => {

    // --- 数据源 (根据最新研究报告) ---
    const BRAINROT_DATA = [
        // Common Brainrots (Beginner Tier)
        { name: 'Noob<PERSON> Pizzanini', emoji: '🍕🧑', rarity: 'Common', cost: 25, income: 1 },
        { name: '<PERSON><PERSON><PERSON>', emoji: '🎵🍃', rarity: 'Common', cost: 250, income: 3 },
        { name: 'Tim Cheese', emoji: '🧀👦', rarity: 'Common', cost: 500, income: 5 },
        { name: 'FluriFlura', emoji: '🌸🌿', rarity: 'Common', cost: 750, income: 7 },
        { name: '<PERSON><PERSON><PERSON>', emoji: '🐾⛏️', rarity: 'Common', cost: 1000, income: 9 },
        { name: 'Svinina Bombardino', emoji: '🐷💣', rarity: 'Common', cost: 1200, income: 10 },
        { name: '<PERSON><PERSON>', emoji: '🥝🐦', rarity: 'Common', cost: 1500, income: 13 },

        // Rare Brainrots (Mid Game)
        { name: '<PERSON><PERSON> Troppi', emoji: '🦶🎩', rarity: 'Rare', cost: 2000, income: 15 },
        { name: 'Tung Tung Tung Sahur', emoji: '🥁🔔', rarity: 'Rare', cost: 3000, income: 25 },
        { name: 'Gangster Footera', emoji: '👞🕶', rarity: 'Rare', cost: 4000, income: 30 },
        { name: 'Bandito Bobritto', emoji: '🌯🤠', rarity: 'Rare', cost: 4500, income: 35 },
        { name: 'Boneca Ambalabu', emoji: '🦴🪆', rarity: 'Rare', cost: 5000, income: 40 },
        { name: 'Cacto Hipopotamo', emoji: '🌵🦛', rarity: 'Rare', cost: 6500, income: 50 },
        { name: 'Ta Ta Ta Ta Sahur', emoji: '🎷🔔', rarity: 'Rare', cost: 7500, income: 55 },
        { name: 'Tric Trac Baraboom', emoji: '💥🎪', rarity: 'Rare', cost: 9000, income: 65 },
        { name: 'Cappuccino Assassino', emoji: '☕🗡️', rarity: 'Rare', cost: 10000, income: 75 },
        { name: 'Brr Brr Patapim', emoji: '🥶🥁', rarity: 'Rare', cost: 15000, income: 100 },
        { name: 'Trulimero Trulicina', emoji: '🎩👑', rarity: 'Rare', cost: 20000, income: 125 },
        { name: 'Bambini Crostini', emoji: '👶🍞', rarity: 'Rare', cost: 22500, income: 130 },
        { name: 'Bananita Dolphinita', emoji: '🍌🐬', rarity: 'Rare', cost: 25000, income: 150 },
        { name: 'Perochello Lemonchello', emoji: '🍋🎻', rarity: 'Rare', cost: 27500, income: 160 },
        { name: 'Brri Brri Bicus Dicus Bombicus', emoji: '🧀💥', rarity: 'Rare', cost: 30000, income: 175 },

        // Epic Brainrots (High-Earning Tier)
        { name: 'Avocadini Guffo', emoji: '🥑🦉', rarity: 'Epic', cost: 35000, income: 225 },
        { name: 'Salamino Penguino', emoji: '🐧🍖', rarity: 'Epic', cost: 40000, income: 250 },
        { name: 'Burbaloni Loliloli', emoji: '🍬🫧', rarity: 'Epic', cost: 35000, income: 200 },
        { name: 'Chimpazini Bananini', emoji: '🐒🍌', rarity: 'Epic', cost: 50000, income: 300 },
        { name: 'Ballerina Cappuccina', emoji: '🩰☕', rarity: 'Epic', cost: 100000, income: 500 },
        { name: 'Chef Crabracadabra', emoji: '🦀🧑‍🍳', rarity: 'Epic', cost: 150000, income: 600 },
        { name: 'Lionel Cactuseli', emoji: '🦁🌵', rarity: 'Epic', cost: 175000, income: 650 },
        { name: 'Glorbo Fruttodrillo', emoji: '🥝🐊', rarity: 'Epic', cost: 200000, income: 750 },
        { name: 'Blueberrini Octopusin', emoji: '🫐🐙', rarity: 'Epic', cost: 250000, income: 1000 },
        { name: 'Strawberelli Flamingelli', emoji: '🍓🦩', rarity: 'Epic', cost: 275000, income: 1100 },
        { name: 'Pandaccini Bananini', emoji: '🐼🍌', rarity: 'Epic', cost: 300000, income: 1200 },

        // Legendary Brainrots (Elite Tier)
        { name: 'Frigo Camelo', emoji: '🧊🐫', rarity: 'Legendary', cost: 300000, income: 1400 },
        { name: 'Orangutini Ananassini', emoji: '🦧🍍', rarity: 'Legendary', cost: 400000, income: 1700 },
        { name: 'Rhino Toasterino', emoji: '🦏🍞', rarity: 'Legendary', cost: 450000, income: 2100 },
        { name: 'Bombardiro Crocodilo', emoji: '💣🐊', rarity: 'Legendary', cost: 500000, income: 2500 },
        { name: 'Spioniro Golubiro', emoji: '🕵️🐦', rarity: 'Legendary', cost: 750000, income: 3500 },
        { name: 'Bombombini Gusini', emoji: '💣🐛', rarity: 'Legendary', cost: 1000000, income: 5000 },
        { name: 'Zibra Zubra Zibralini', emoji: '🦓🎪', rarity: 'Legendary', cost: 1000000, income: 6000 },
        { name: 'Tigrilini Watermelini', emoji: '🐅🍉', rarity: 'Legendary', cost: 1000000, income: 6500 },
        { name: 'Cavallo Virtuso', emoji: '🐎🎭', rarity: 'Legendary', cost: 2500000, income: 7500 },
        { name: 'Gorillo Watermelondrillo', emoji: '🦍🍉', rarity: 'Legendary', cost: 3000000, income: 8000 },

        // Mythic Brainrots (Top Tier)
        { name: 'Coco Elefanto', emoji: '🐘🥥', rarity: 'Mythic', cost: 5000000, income: 10000 },
        { name: 'Girafa Celestre', emoji: '🦒⭐', rarity: 'Mythic', cost: 7500000, income: 20000 },
        { name: 'Gattatino Nyanino', emoji: '🐱🌟', rarity: 'Mythic', cost: 7500000, income: 35000 },
        { name: 'Matteo', emoji: '👨‍🎤🎸', rarity: 'Mythic', cost: 10000000, income: 50000 },
        { name: 'Tralalero Tralala', emoji: '🎺🎶', rarity: 'Mythic', cost: 10000000, income: 50000 },
        { name: 'Trigoligre Frutonni', emoji: '🐅🍊', rarity: 'Mythic', cost: 15000000, income: 60000 },
        { name: 'Espresso Signora', emoji: '☕👸', rarity: 'Mythic', cost: 25000000, income: 70000 },
        { name: 'Odin Din Din Dun', emoji: '🦉🔔', rarity: 'Mythic', cost: 15000000, income: 75000 },
        { name: 'Statutino Libertino', emoji: '🗽⚖️', rarity: 'Mythic', cost: 20000000, income: 75000 },
        { name: 'Orcalero Orcala', emoji: '🐋🎪', rarity: 'Mythic', cost: 15000000, income: 100000 },

        // Brainrot God Tier (Endgame)
        { name: 'Trenostruzzo Turbo 3000', emoji: '🚂🦓', rarity: 'God', cost: 25000000, income: 150000 },
        { name: 'Ballerino Lololo', emoji: '💃🎭', rarity: 'God', cost: 35000000, income: 200000 },
        { name: 'Los Crocodillitos', emoji: '🐊👥', rarity: 'God', cost: 12500000, income: 55000 },
        { name: 'Las Vaquitas Saturnitas', emoji: '🐮🪐', rarity: 'God', cost: 160000000, income: 750000 },

        // Secret Brainrots (Rarest in Game)
        { name: 'La Vacca Staturno Saturnita', emoji: '🐮🪐', rarity: 'Secret', cost: 50000000, income: 250000 },
        { name: 'Chimpanzini Spiderini', emoji: '🐒🕷️', rarity: 'Secret', cost: 100000000, income: 325000 },
        { name: 'Tortuginni Dragonfruitini', emoji: '🐢🐉', rarity: 'Secret', cost: 500000000, income: 350000 },
        { name: 'Los Tralaleritos', emoji: '🎸🎤', rarity: 'Secret', cost: 150000000, income: 500000 },
        { name: 'Las Tralaleritas', emoji: '🎵👥', rarity: 'Secret', cost: 150000000, income: 650000 },
        { name: 'Graipuss Medussi', emoji: '🍇🐍', rarity: 'Secret', cost: 250000000, income: 1000000 },
        { name: 'Pot Hotspot', emoji: '🍯🔥', rarity: 'Secret', cost: 500000000, income: 2500000 },
        { name: 'Chicleteira Bicicleteira', emoji: '🚲🍬', rarity: 'Secret', cost: 125000000, income: 3500000 },
        { name: 'La Grande Combinasion', emoji: '✨🤖', rarity: 'Secret', cost: 1000000000, income: 10000000 },
        { name: 'Nuclearo Dinossauro', emoji: '☢️🦕', rarity: 'Secret', cost: 2500000000, income: 15000000 },
        { name: 'Garama and Madundung', emoji: '👥🌟', rarity: 'Secret', cost: 10000000000, income: 50000000 }
    ];

    const MUTATION_DATA = [
        { name: 'Regular', displayName: 'Regular', multiplier: 1.0, color: '#ffffff' },
        { name: 'Gold', displayName: 'Gold', multiplier: 1.25, color: '#ffd700' },
        { name: 'Diamond', displayName: 'Diamond', multiplier: 1.5, color: '#b9f2ff' },
        { name: 'Galactic', displayName: 'Galactic', multiplier: 3.0, color: '#9d4edd' },
        { name: 'Blood', displayName: 'Blood', multiplier: 2.0, color: '#dc2626' },
        { name: 'Candy', displayName: 'Candy', multiplier: 4.0, color: '#f472b6' },
        { name: 'Lava', displayName: 'Lava', multiplier: 6.0, color: '#f97316' },
        { name: 'Rainbow', displayName: 'Rainbow', multiplier: 10.0, color: 'linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3)' }
    ];

    // 基础变异概率 (不考虑服务器幸运值和血月事件)
    const BASE_MUTATION_CHANCES = {
        'Regular': 80.0,
        'Gold': 12.0,
        'Diamond': 6.0,
        'Galactic': 1.5,
        'Blood': 0.5,
        'Candy': 0.0,  // 仅在特定事件期间
        'Lava': 0.0,   // 仅在特定事件期间
        'Rainbow': 0.0 // 极其稀有
    };

    // --- DOM 元素获取 ---
    const brainrotSelect = document.getElementById('brainrot-select');
    const serverLuckSelect = document.getElementById('server-luck-select');
    const bloodmoonCheckbox = document.getElementById('bloodmoon-checkbox');
    const mutationButtons = document.querySelectorAll('.mutation-btn');

    // 结果显示区域
    const brainrotDisplay = document.getElementById('brainrot-display');
    const costDisplay = document.getElementById('cost-display');
    const sellValueDisplay = document.getElementById('sell-value-display');
    const mutationDisplay = document.getElementById('mutation-display');
    const incomeDisplay = document.getElementById('income-display');
    const profitHourDisplay = document.getElementById('profit-hour-display');
    const breakEvenDisplay = document.getElementById('break-even-display');
    const mutateCostDisplay = document.getElementById('mutate-cost-display');

    // 变异概率表格
    const mutationChancesTable = document.getElementById('mutation-chances-table');
    const serverLuckDisplay = document.getElementById('server-luck-display');

    // 提示区域
    const tipDisplay = document.getElementById('tip-display');

    // --- 核心功能 ---

    // 当前选择的状态
    let currentBrainrotIndex = 0;
    let currentMutation = 'Regular';
    let currentServerLuck = 1;
    let isBloodmoonActive = false;

    // 计算变异概率
    function calculateMutationChances() {
        const chances = { ...BASE_MUTATION_CHANCES };

        // 应用服务器幸运值
        if (currentServerLuck > 1) {
            // 幸运值主要影响稀有变异
            chances.Blood *= currentServerLuck;
            chances.Galactic *= currentServerLuck * 0.8;
            chances.Diamond *= currentServerLuck * 0.6;
            chances.Gold *= currentServerLuck * 0.4;
        }

        // 血月事件大幅提升Blood变异概率
        if (isBloodmoonActive) {
            chances.Blood *= 5;
        }

        // 重新标准化概率，确保总和为100%
        const total = Object.values(chances).reduce((sum, chance) => sum + chance, 0);
        const normalizedChances = {};
        for (const [mutation, chance] of Object.entries(chances)) {
            normalizedChances[mutation] = (chance / total) * 100;
        }

        return normalizedChances;
    }

    // 格式化数字（例如 1,000,000）
    function formatNumber(num) {
        if (num >= 1000000000) {
            return (num / 1000000000).toFixed(1) + 'B';
        } else if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
    }

    // 格式化时间（秒 -> 天/小时/分钟/秒）
    function formatTime(totalSeconds) {
        if (totalSeconds === Infinity || isNaN(totalSeconds) || totalSeconds <= 0) {
            return 'N/A';
        }

        const days = Math.floor(totalSeconds / 86400);
        totalSeconds %= 86400;
        const hours = Math.floor(totalSeconds / 3600);
        totalSeconds %= 3600;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        let result = '';
        if (days > 0) result += `${days}d `;
        if (hours > 0) result += `${hours}h `;
        if (minutes > 0) result += `${minutes}m `;
        if (seconds > 0 || result === '') result += `${seconds}s`;

        return result.trim();
    }

    // 更新显示
    function updateDisplay() {
        const brainrot = BRAINROT_DATA[currentBrainrotIndex];
        const mutation = MUTATION_DATA.find(m => m.name === currentMutation);

        if (!brainrot || !mutation) return;

        // 计算收入
        const baseIncome = brainrot.income;
        const finalIncome = baseIncome * mutation.multiplier;
        const profitPerHour = finalIncome * 3600;
        const sellValue = Math.floor(brainrot.cost * 0.5); // 假设卖出价值为成本的50%
        const breakEvenTime = brainrot.cost / finalIncome;
        const mutateCost = 5000000; // 固定变异成本

        // 更新Brainrot显示
        brainrotDisplay.innerHTML = `
            <div class="brainrot-info">
                <div class="brainrot-emoji">${brainrot.emoji}</div>
                <div class="brainrot-rarity">${brainrot.rarity}</div>
            </div>
            <div class="brainrot-name">${brainrot.name}</div>
        `;

        // 更新统计信息
        costDisplay.textContent = `$${formatNumber(brainrot.cost)}`;
        sellValueDisplay.textContent = `$${formatNumber(sellValue)}`;
        mutationDisplay.textContent = mutation.displayName;
        mutationDisplay.style.color = mutation.color;
        incomeDisplay.textContent = `$${formatNumber(finalIncome)}`;
        profitHourDisplay.textContent = `$${formatNumber(profitPerHour)}`;
        breakEvenDisplay.textContent = formatTime(breakEvenTime);
        mutateCostDisplay.textContent = `$${formatNumber(mutateCost)}`;

        // 更新变异概率表格
        updateMutationChancesTable();

        // 更新提示
        updateTip(brainrot);
    }

    // 更新变异概率表格
    function updateMutationChancesTable() {
        const chances = calculateMutationChances();
        const tbody = mutationChancesTable.querySelector('tbody');
        tbody.innerHTML = '';

        for (const [mutationName, chance] of Object.entries(chances)) {
            if (chance > 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${mutationName}</td>
                    <td>${chance.toFixed(2)}%</td>
                `;
                tbody.appendChild(row);
            }
        }

        serverLuckDisplay.textContent = `${currentServerLuck}×`;
    }

    // 更新提示
    function updateTip(brainrot) {
        let tip = '';

        if (brainrot.rarity === 'Common') {
            tip = '💡 Starter Brainrot. Use at the very beginning.';
        } else if (brainrot.rarity === 'Rare') {
            tip = '⚡ Good mid-game choice for steady income.';
        } else if (brainrot.rarity === 'Epic') {
            tip = '🔥 High-earning tier. Great for late game.';
        } else if (brainrot.rarity === 'Legendary') {
            tip = '⭐ Elite tier. Excellent investment.';
        } else if (brainrot.rarity === 'Mythic') {
            tip = '🌟 Top tier. Massive income potential.';
        } else if (brainrot.rarity === 'God') {
            tip = '👑 Endgame tier. Ultimate power.';
        } else if (brainrot.rarity === 'Secret') {
            tip = '🎯 Rarest in game. Incredible income.';
        }

        tipDisplay.innerHTML = tip;
    }

    // 格式化数字（例如 1,000,000）
    function formatNumber(num) {
        return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
    }

    // 格式化时间（秒 -> 天/小时/分钟/秒）
    function formatTime(totalSeconds) {
        if (totalSeconds === Infinity || isNaN(totalSeconds) || totalSeconds <= 0) {
            return 'N/A';
        }

        const days = Math.floor(totalSeconds / 86400);
        totalSeconds %= 86400;
        const hours = Math.floor(totalSeconds / 3600);
        totalSeconds %= 3600;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        let result = '';
        if (days > 0) result += `${days}天 `;
        if (hours > 0) result += `${hours}小时 `;
        if (minutes > 0) result += `${minutes}分钟 `;
        if (seconds > 0 || result === '') result += `${seconds}秒`;

        return result.trim();
    }
    
    // 事件处理器
    function handleBrainrotChange() {
        currentBrainrotIndex = parseInt(brainrotSelect.value);
        updateDisplay();
    }

    function handleServerLuckChange() {
        currentServerLuck = parseInt(serverLuckSelect.value);
        updateDisplay();
    }

    function handleBloodmoonChange() {
        isBloodmoonActive = bloodmoonCheckbox.checked;
        updateDisplay();
    }

    function handleMutationChange(mutationName) {
        currentMutation = mutationName;

        // 更新按钮状态
        mutationButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.mutation === mutationName) {
                btn.classList.add('active');
            }
        });

        updateDisplay();
    }

    // --- 初始化和事件监听 ---
    function initialize() {
        // 填充 Brainrot 下拉框
        BRAINROT_DATA.forEach((data, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = `${data.emoji} ${data.name} [${data.rarity}]`;
            brainrotSelect.appendChild(option);
        });

        // 设置事件监听
        brainrotSelect.addEventListener('change', handleBrainrotChange);
        serverLuckSelect.addEventListener('change', handleServerLuckChange);
        bloodmoonCheckbox.addEventListener('change', handleBloodmoonChange);

        // 设置变异按钮事件
        mutationButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                handleMutationChange(btn.dataset.mutation);
            });
        });

        // 初始状态
        currentBrainrotIndex = 0;
        currentMutation = 'Regular';
        currentServerLuck = 1;
        isBloodmoonActive = false;

        // 设置默认选中的变异按钮
        mutationButtons.forEach(btn => {
            if (btn.dataset.mutation === 'Regular') {
                btn.classList.add('active');
            }
        });

        // 初始显示更新
        updateDisplay();
    }

    // 启动计算器
    initialize();
});